{"name": "console-databuilder", "version": "1.0.0", "description": "大数据 DataBuilder 产品前端项目", "main": "src/index.tsx", "scripts": {"start": "npm run dev:mock", "dev": "cba-cli dev", "dev:private": "cba-cli dev --config=private-common/bce-config.js --hasFlags", "dev:mock": "cba-cli dev --mock", "build": "cba-cli build", "build:private": "cba-cli build --config=private-common/bce-config.js --hasFlags", "i18n:extract": "cba-cli i18n:extract", "i18n:upload": "cba-cli i18n:upload", "svg": "svgo ./src/assets/svg/*.svg"}, "devDependencies": {"@baidu/cba-cli": "^1.2.8-beta.13", "@baidu/cba-preset-console-react": "^1.2.8-beta.13", "@baidu/cba-preset-react": "^1.2.8-beta.13", "@baiducloud/i18n": "1.0.0-rc.29", "@types/lodash": "^4.14.202", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "copy-webpack-plugin": "^13.0.0", "eslint-plugin-prettier": "^5.1.3", "postcss-less": "^6.0.0", "prettier-eslint": "^16.3.0", "stylelint": "^16.0.2", "stylelint-config-standard-less": "^3.0.1", "svgo": "^3.3.2", "tailwindcss": "^3.4.17"}, "dependencies": {"@ahooksjs/use-url-state": "^3.5.1", "@antv/x6": "^2.18.1", "@antv/x6-plugin-export": "^2.1.6", "@antv/x6-plugin-minimap": "^2.0.7", "@antv/x6-plugin-scroller": "^2.0.10", "@antv/x6-react-shape": "^2.2.3", "@baidu/bce-react-toolkit": "0.0.33-beta.1", "@baidu/bos-react-sdk": "0.0.0-beta.27", "@baidu/xicon-react-bigdata": "^0.0.1", "@reduxjs/toolkit": "^2.5.1", "acud": "^1.4.37", "ahooks": "^3.7.8", "axios": "^1.6.5", "classnames": "^2.5.1", "echarts": "^5.4.3", "lodash": "^4.17.21", "moment": "^2.30.1", "monaco-editor": "^0.52.2", "monaco-editor-webpack-plugin": "^7.1.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-monaco-editor": "^0.58.0", "react-redux": "^9.2.0", "react-router-dom": "^6.21.1"}, "engines": {"node": ">=20.0.0", "npm": ">=9.0.0"}}